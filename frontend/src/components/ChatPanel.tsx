'use client';

import { useEffect, useRef, useState } from 'react';
import MessageInput from './MessageInput';
import { playAudio } from '../utils/audio';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  text: string;
  audioUrl?: string;
  imageUrls?: string[];
  timestamp: Date;
}

interface ChatPanelProps {
  messages: ChatMessage[];
  isProcessing: boolean;
  onSendMessage: (text: string, images?: File[]) => void;
  onInputFocusChange: (isFocused: boolean) => void;
  streamTranscription: string;
  isStreaming: boolean;
}

const ChatPanel = ({ 
  messages, 
  isProcessing, 
  onSendMessage, 
  onInputFocusChange,
  streamTranscription,
  isStreaming
}: ChatPanelProps) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);

  // Yeni mesaj geldi<PERSON>inde otomatik scroll
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Ses çalma fonksiyonu
  const playAudioMessage = async (audioUrl: string, messageId: string) => {
    try {
      if (playingAudio && playingAudio !== messageId) {
        setPlayingAudio(null);
      }

      if (playingAudio === messageId) {
        setPlayingAudio(null);
        return;
      }

      setPlayingAudio(messageId);
      await playAudio(audioUrl);
      setPlayingAudio(null);
    } catch (error) {
      setPlayingAudio(null);
      console.error('Ses çalma hatası:', error);
    }
  };

  return (
    <div className="h-full flex flex-col bg-black/20 backdrop-blur-sm rounded-2xl border border-white/10">
      {/* Header */}
      <div className="p-6 border-b border-white/10 flex-shrink-0">
        <h2 className="text-xl font-semibold text-white mb-2">Konuşma Geçmişi</h2>
        <p className="text-gray-300 text-sm">AI asistanınızla olan diyaloglarınız</p>
      </div>

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4 min-h-0">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="w-16 h-16 rounded-full bg-white/10 flex items-center justify-center mb-4">
              <span className="text-2xl">💬</span>
            </div>
            <h3 className="text-lg font-medium text-white mb-2">
              Henüz mesaj yok
            </h3>
            <p className="text-gray-400 text-sm max-w-xs">
              Konuşmaya başlamak için sol paneldeki mikrofonu kullanın
            </p>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] px-4 py-3 rounded-2xl ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white ml-4'
                      : 'bg-white/10 backdrop-blur-sm text-white mr-4 border border-white/20'
                  }`}
                >
                  {/* Avatar */}
                  <div className={`flex items-start space-x-3 ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                      message.type === 'user' 
                        ? 'bg-blue-700' 
                        : 'bg-white/20'
                    }`}>
                      {message.type === 'user' ? '👤' : '🤖'}
                    </div>
                    
                    <div className="flex-1">
                      {/* Message Images */}
                      {message.imageUrls && message.imageUrls.length > 0 && (
                        <div className="mb-3 flex flex-wrap gap-2">
                          {message.imageUrls.map((imageUrl, index) => (
                            <img
                              key={index}
                              src={imageUrl}
                              alt={`Gönderilen görsel ${index + 1}`}
                              className="max-w-64 max-h-64 rounded-lg border border-white/20 cursor-pointer hover:opacity-80 transition-opacity"
                              onClick={() => window.open(imageUrl, '_blank')}
                            />
                          ))}
                        </div>
                      )}

                      {/* Message Text */}
                      {message.text && (
                        <p className="text-sm leading-relaxed">{message.text}</p>
                      )}

                      {/* Audio Play Button */}
                      {message.audioUrl && (
                        <div className="mt-3">
                          <button
                            onClick={() => playAudioMessage(message.audioUrl!, message.id)}
                            className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors border border-white/20"
                            title="Yanıtı sesli dinle"
                          >
                            <span className="text-lg">
                              {playingAudio === message.id ? '⏸️' : '🔊'}
                            </span>
                            <span className="text-xs text-gray-300">
                              {playingAudio === message.id ? 'Duraklatmak için tıkla' : 'Sesli dinle'}
                            </span>
                          </button>
                        </div>
                      )}
                      
                      {/* Timestamp */}
                      <p className={`text-xs mt-2 ${
                        message.type === 'user' 
                          ? 'text-blue-200' 
                          : 'text-gray-400'
                      }`}>
                        {message.timestamp.toLocaleString('tr-TR', {
                          weekday: 'short',
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                          hour: '2-digit',
                          minute: '2-digit',
                          hour12: false
                        })}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            
            {/* Processing Indicator */}
            {(isProcessing || isStreaming) && (
              <div className="flex justify-start">
                <div className="bg-white/10 backdrop-blur-sm text-white mr-4 border border-white/20 px-4 py-3 rounded-2xl max-w-[80%]">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 rounded-full flex items-center justify-center text-sm bg-white/20">
                      🤖
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                        <span className="text-sm text-gray-300">{isStreaming ? 'Ses aktarılıyor...' : 'AI düşünüyor...'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message Input */}
      <div className="flex-shrink-0">
        <MessageInput
          onSendMessage={onSendMessage}
          isProcessing={isProcessing || isStreaming}
          onFocusChange={onInputFocusChange}
          streamTranscription={streamTranscription}
        />
      </div>
    </div>
  );
};

export default ChatPanel;
